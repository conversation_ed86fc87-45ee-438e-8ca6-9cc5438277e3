class_name ProjectSelector
extends Control

## 项目选择器组件
## 实现无限循环轮播选择器，每次最多显示5个项目，选中项目始终位于中间位置

signal project_selected(project: ProjectData)

@onready var projects_container: HBoxContainer = $VBoxContainer/ProjectsContainer

var projects: Array[ProjectData] = []
var current_index: int = 0
var project_buttons: Array[Button] = []

const PROJECT_BUTTON_WIDTH = 120
const PROJECT_BUTTON_HEIGHT = 60
const MAX_VISIBLE_PROJECTS = 5  # 最多显示5个项目
const CENTER_POSITION = 2  # 中心位置索引（0-4中的第2个）
const DRAG_THRESHOLD = 30  # 拖拽阈值（像素）
const DRAG_FEEDBACK_THRESHOLD = 10  # 拖拽反馈阈值（像素）

func _ready() -> void:
	# 初始化固定的5个项目按钮位置
	_initialize_project_buttons()

## 设置项目列表
## [br]更新显示的项目列表并刷新显示
func set_projects(project_list: Array[ProjectData]) -> void:
	projects = project_list

	# 确保选中索引有效
	if projects.size() > 0:
		current_index = clamp(current_index, 0, projects.size() - 1)
	else:
		current_index = 0

	# 更新显示的项目
	_update_visible_projects()

## 获取当前选中的项目
func get_current_project() -> ProjectData:
	if current_index >= 0 and current_index < projects.size():
		return projects[current_index]
	return null

## 设置当前选中的项目索引
func set_current_index(index: int) -> void:
	if index >= 0 and index < projects.size():
		current_index = index
		_update_visible_projects()

## 初始化固定的5个项目按钮位置
func _initialize_project_buttons() -> void:
	# 清除现有按钮
	for button in project_buttons:
		button.queue_free()
	project_buttons.clear()

	# 创建固定的5个按钮位置
	for i in range(MAX_VISIBLE_PROJECTS):
		var button = _create_empty_project_button(i)
		projects_container.add_child(button)
		project_buttons.append(button)

## 创建空的项目按钮
func _create_empty_project_button(position_index: int) -> Button:
	var button = Button.new()
	button.text = ""
	button.custom_minimum_size = Vector2(PROJECT_BUTTON_WIDTH, PROJECT_BUTTON_HEIGHT)
	button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	button.visible = false  # 初始隐藏

	# 连接点击信号，传递位置索引
	button.pressed.connect(_on_position_button_pressed.bind(position_index))

	# 连接按钮的拖拽事件
	button.gui_input.connect(_on_button_gui_input.bind(position_index))

	return button

## 更新可见项目显示
## [br]根据当前选中索引，计算并显示5个位置的项目
func _update_visible_projects() -> void:
	if projects.is_empty():
		# 没有项目时隐藏所有按钮
		for button in project_buttons:
			button.visible = false
		return

	# 计算每个位置应该显示的项目索引
	var visible_count = min(projects.size(), MAX_VISIBLE_PROJECTS)

	for pos in range(MAX_VISIBLE_PROJECTS):
		var button = project_buttons[pos]

		if pos < visible_count:
			# 计算该位置对应的项目索引
			var project_index = _get_project_index_for_position(pos)
			var project = projects[project_index]

			# 更新按钮内容和样式
			button.text = project.name
			button.visible = true

			# 设置选中状态样式
			if pos == CENTER_POSITION:
				# 中心位置（选中状态）
				button.add_theme_stylebox_override("normal", _get_selected_style())
				button.scale = Vector2(1.2, 1.2)
			else:
				# 普通状态
				button.remove_theme_stylebox_override("normal")
				button.scale = Vector2(1.0, 1.0)
		else:
			# 隐藏多余的按钮位置
			button.visible = false

	# 发送选中信号
	if current_index >= 0 and current_index < projects.size():
		project_selected.emit(projects[current_index])

## 根据位置索引计算对应的项目索引
## [br]实现环形循环逻辑
func _get_project_index_for_position(position: int) -> int:
	if projects.is_empty():
		return 0

	# 计算相对于中心位置的偏移
	var offset = position - CENTER_POSITION
	# 应用环形循环
	var project_index = (current_index + offset + projects.size()) % projects.size()
	return project_index

## 更新选中状态（保持兼容性）
func _update_selection() -> void:
	# 新的实现直接调用更新可见项目方法
	_update_visible_projects()

## 获取选中状态的样式
func _get_selected_style() -> StyleBox:
	var style = StyleBoxFlat.new()
	style.bg_color = Color.BLUE
	style.border_width_left = 2
	style.border_width_right = 2
	style.border_width_top = 2
	style.border_width_bottom = 2
	style.border_color = Color.WHITE
	return style

## 切换到下一个项目（支持循环）
func next_project() -> void:
	if projects.size() > 0:
		current_index = (current_index + 1) % projects.size()
		_animate_project_change(true)

## 切换到上一个项目（支持循环）
func previous_project() -> void:
	if projects.size() > 0:
		current_index = (current_index - 1 + projects.size()) % projects.size()
		_animate_project_change(false)

## 项目切换动画
## [br]direction: true为向右切换，false为向左切换
func _animate_project_change(direction_right: bool) -> void:
	# 创建切换动画效果
	var tween = create_tween()
	tween.set_parallel(true)

	# 为所有可见按钮添加滑动效果
	for i in range(project_buttons.size()):
		var button = project_buttons[i]
		if button.visible:
			var start_pos = button.position
			var offset = Vector2(50 if direction_right else -50, 0)

			# 先移动到偏移位置
			tween.tween_property(button, "position", start_pos + offset, 0.15)
			# 然后移动回原位置
			tween.tween_property(button, "position", start_pos, 0.15).set_delay(0.15)

	# 动画完成后更新显示
	tween.tween_callback(_update_visible_projects).set_delay(0.1)

## 处理拖拽输入
func _gui_input(event: InputEvent) -> void:
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			if event.pressed:
				_start_drag(event.global_position)
			else:
				_end_drag(event.global_position)
	elif event is InputEventMouseMotion and _is_dragging:
		_update_drag(event.global_position)

var _is_dragging: bool = false
var _drag_start_pos: Vector2

## 开始拖拽
func _start_drag(pos: Vector2) -> void:
	_is_dragging = true
	_drag_start_pos = pos

	# 保存所有按钮的原始位置
	for button in project_buttons:
		if button.visible:
			button.set_meta("original_position", button.position)

## 更新拖拽
func _update_drag(pos: Vector2) -> void:
	if not _is_dragging:
		return

	# 计算拖拽距离和方向
	var delta = _drag_start_pos.x - pos.x
	var abs_delta = abs(delta)

	# 显示拖拽反馈
	if abs_delta > DRAG_FEEDBACK_THRESHOLD:
		_show_drag_feedback(delta > 0, abs_delta)

## 结束拖拽
func _end_drag(pos: Vector2) -> void:
	if not _is_dragging:
		return

	_is_dragging = false

	# 重置所有按钮位置（清除拖拽反馈）
	_reset_button_positions()

	# 检查是否需要切换项目
	var drag_distance = abs(_drag_start_pos.x - pos.x)
	if drag_distance > DRAG_THRESHOLD:
		if pos.x < _drag_start_pos.x:
			# 向左拖拽，切换到下一个项目
			next_project()
		else:
			# 向右拖拽，切换到上一个项目
			previous_project()

## 位置按钮点击事件
## [br]根据点击的位置计算目标项目并切换
func _on_position_button_pressed(position_index: int) -> void:
	if projects.is_empty():
		return

	# 计算点击位置对应的项目索引
	var target_project_index = _get_project_index_for_position(position_index)

	# 如果点击的不是中心位置，则切换到该项目
	if position_index != CENTER_POSITION:
		current_index = target_project_index
		_animate_project_change(position_index > CENTER_POSITION)
	# 如果点击的是中心位置，重新发送选中信号（可用于确认选择）
	else:
		project_selected.emit(projects[current_index])

## 显示拖拽反馈
## [br]direction_right: true表示向右拖拽，false表示向左拖拽
## [br]drag_distance: 拖拽距离，用于计算反馈强度
func _show_drag_feedback(direction_right: bool, drag_distance: float) -> void:
	# 计算反馈强度（最大偏移20像素）
	var max_offset = 20.0
	var offset_strength = min(drag_distance / 100.0, 1.0)  # 100像素为最大强度
	var offset_x = (max_offset * offset_strength) * (-1 if direction_right else 1)

	# 为所有可见按钮添加位移反馈
	for i in range(project_buttons.size()):
		var button = project_buttons[i]
		if button.visible:
			# 中心按钮移动最多，两边按钮移动较少
			var distance_from_center = abs(i - CENTER_POSITION)
			var button_offset_factor = 1.0 - (distance_from_center * 0.3)
			var button_offset = Vector2(offset_x * button_offset_factor, 0)

			# 设置按钮位置（相对于原始位置）
			if not button.has_meta("original_position"):
				button.set_meta("original_position", button.position)

			var original_pos = button.get_meta("original_position")
			button.position = original_pos + button_offset

## 重置所有按钮位置
func _reset_button_positions() -> void:
	for button in project_buttons:
		if button.has_meta("original_position"):
			var original_pos = button.get_meta("original_position")
			button.position = original_pos
			button.remove_meta("original_position")

## 处理按钮上的拖拽事件
func _on_button_gui_input(event: InputEvent, position_index: int) -> void:
	# 将按钮上的拖拽事件转发到主控件
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			if event.pressed:
				_start_drag(event.global_position)
			else:
				_end_drag(event.global_position)
	elif event is InputEventMouseMotion and _is_dragging:
		_update_drag(event.global_position)
